local assets = {}
local prefabs = {}

local function ApplySeasonStats(inst, owner)
    if not owner or not owner:IsValid() then return end
    local season = TheWorld and TheWorld.state and TheWorld.state.season or "autumn"

    -- 先清理之前的饥饿调整
    if owner.components and owner.components.hunger and inst._old_rate then
        owner.components.hunger:SetRate(inst._old_rate)
        inst._old_rate = nil
    end

    -- 默认清空，再按季节设置
    if inst.components.waterproofer then
        inst.components.waterproofer:SetEffectiveness(0)
    end
    if inst.components.insulator then
        inst.components.insulator:SetInsulation(0)
        inst.components.insulator.type = nil
    end

    -- 根据季节调整颜色和属性
    if season == "spring" then
        inst.AnimState:SetMultColour(0.7, 1.0, 0.8, 1.0) -- 春季绿色调
        if inst.components.waterproofer then
            inst.components.waterproofer:SetEffectiveness(0.4)
        end
    elseif season == "summer" then
        inst.AnimState:SetMultColour(1.0, 0.8, 0.5, 1.0) -- 夏季橙色调
        if inst.components.insulator then
            -- 夏季隔热：设置夏季隔热值
            inst.components.insulator:SetInsulation(TUNING.CLIMATE_CLOAK_HEAT)
            inst.components.insulator.type = "summer"
        end
    elseif season == "autumn" then
        inst.AnimState:SetMultColour(0.9, 0.7, 0.5, 1.0) -- 秋季褐色调
        -- 饥饿-5%：基于当前饥饿速率进行调整（与角色被动叠加）
        if owner.components and owner.components.hunger then
            inst._old_rate = owner.components.hunger:GetRate()
            -- 在当前速率基础上再减少5%
            owner.components.hunger:SetRate(inst._old_rate * 0.95)
        end
    elseif season == "winter" then
        inst.AnimState:SetMultColour(0.7, 0.9, 1.0, 1.0) -- 冬季蓝色调
        if inst.components.insulator then
            -- 冬季保温：设置冬季保温值
            inst.components.insulator:SetInsulation(TUNING.CLIMATE_CLOAK_COLD)
            inst.components.insulator.type = "winter"
        end
    end
end

local function ClearOwnerAdjust(inst, owner)
    if owner and owner.components and owner.components.hunger and inst._old_rate then
        owner.components.hunger:SetRate(inst._old_rate)
    end
    inst._old_rate = nil
end

local function onequip(inst, owner)
    owner.AnimState:OverrideSymbol("swap_body", "sweatervest", "swap_body") -- 基于trunkvest/夏装
    ApplySeasonStats(inst, owner)
    if inst._seasonwatch ~= nil then inst._seasonwatch:Cancel() end
    inst._seasonwatch = owner:WatchWorldState("season", function()
        ApplySeasonStats(inst, owner)
    end)
end

local function onunequip(inst, owner)
    owner.AnimState:ClearOverrideSymbol("swap_body")
    if inst._seasonwatch ~= nil then
        inst._seasonwatch:Cancel()
        inst._seasonwatch = nil
    end
    ClearOwnerAdjust(inst, owner)
end

local function fn()
    local inst = CreateEntity()
    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("sweatervest")
    inst.AnimState:SetBuild("sweatervest")
    inst.AnimState:PlayAnimation("idle")

    -- 默认颜色（四季融合的中性色调）
    inst.AnimState:SetMultColour(0.9, 0.85, 0.8, 1.0)

    inst.entity:SetPristine()

    inst:AddTag("waterproofer")

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")

    inst:AddComponent("equippable")
    inst.components.equippable.equipslot = GLOBAL.EQUIPSLOTS.BODY
    inst.components.equippable:SetOnEquip(onequip)
    inst.components.equippable:SetOnUnequip(onunequip)

    inst:AddComponent("waterproofer")
    inst.components.waterproofer:SetEffectiveness(0)

    inst:AddComponent("insulator")
    inst.components.insulator:SetInsulation(0)

    return inst
end

return Prefab("climate_cloak", fn, assets, prefabs)
