local assets = {}
local prefabs = {}

local PHASES = {"spring","summer","autumn","winter"}

local function SetShield(inst, on)
    inst._shielded = on and true or false
    if inst._shielded then
        -- 护冠开启：上色提示
        inst.AnimState:SetAddColour(0.15, 0.15, 0.35, 0)
        -- 添加异常免疫标签
        inst:AddTag("season_shield_immune")
    else
        inst.AnimState:SetAddColour(0, 0, 0, 0)
        inst._weapon_hits = 0
        -- 移除异常免疫标签
        inst:RemoveTag("season_shield_immune")
    end
end

local function Slow(target, mult, dur)
    if target and target:IsValid() then
        if not target.components.seasonal_debuff then
            target:AddComponent("seasonal_debuff")
        end
        target.components.seasonal_debuff:ApplySlow(mult, dur, true) -- true表示来自Boss
    end
end

local function OnSigil(inst, data)
    -- 季芯炸符破盾：需要季节匹配
    if data and data.season and inst._phase == data.season and inst._shielded then
        SetShield(inst, false)
        if inst.SoundEmitter then inst.SoundEmitter:PlaySound("dontstarve/common/ghost_spawn") end
        local fx = SpawnPrefab("staff_castinglight")
        if fx then fx.Transform:SetPosition(inst.Transform:GetWorldPosition()) end
    end

    -- 武器三连破盾：季节之刃连击3次（需要季节刻印匹配）
    if data and data.source == "weapon_burst" then
        local t = GetTime()
        if inst._weapon_last and (t - inst._weapon_last) > (TUNING.SEASON_BOSS_WEAPON_BREAK_WINDOW or 10) then
            inst._weapon_hits = 0
        end
        inst._weapon_hits = (inst._weapon_hits or 0) + 1
        inst._weapon_last = t
        if inst._weapon_hits >= 3 and inst._shielded then
            SetShield(inst, false)
            if inst.SoundEmitter then inst.SoundEmitter:PlaySound("dontstarve/common/ghost_spawn") end
            local fx2 = SpawnPrefab("staff_castinglight")
            if fx2 then fx2.Transform:SetPosition(inst.Transform:GetWorldPosition()) end
        end
    end
end

local function Retarget(inst)
    -- 入侵Boss在预警期间不攻击
    if inst._invasion_peaceful then
        return nil
    end
    return FindEntity(inst, 20, function(guy)
        return inst.components.combat:CanTarget(guy)
    end, {"player"}, {"playerghost"})
end
-- 创建藤蔓缠绕点（春季技能）
local function CreateVineTrap(x, z)
    local trap = SpawnPrefab("trap_teeth")  -- 复用陷阱作为基础
    if trap then
        trap.Transform:SetPosition(x, 0, z)
        trap:AddTag("vine_trap")
        -- 修改陷阱效果为减速而非伤害
        if trap.components.trap then
            trap.components.trap.onsprung = function(inst, target)
                if target and target:HasTag("player") then
                    Slow(target, 0.7, 3)  -- 30%减速，持续3秒
                end
                inst:DoTaskInTime(0.1, inst.Remove)
            end
        end
        -- 3秒后自动消失
        trap:DoTaskInTime(3, trap.Remove)
    end
end

-- 创建落叶旋风（秋季技能）
local function CreateLeafWhirlwind(inst, target_x, target_z)
    local whirlwind = SpawnPrefab("staff_castinglight")  -- 复用特效作为旋风
    if whirlwind then
        whirlwind.Transform:SetPosition(target_x, 0, target_z)
        whirlwind:AddTag("leaf_whirlwind")

        -- 旋风追逐逻辑
        local chase_time = 3
        local start_time = GetTime()
        whirlwind:DoPeriodicTask(0.1, function()
            if GetTime() - start_time > chase_time then
                whirlwind:Remove()
                return
            end

            -- 寻找最近玩家
            local nearest = FindEntity(whirlwind, 15, function(guy)
                return guy:HasTag("player") and not guy:HasTag("playerghost")
            end)

            if nearest then
                local wx, wy, wz = whirlwind.Transform:GetWorldPosition()
                local px, py, pz = nearest.Transform:GetWorldPosition()
                local dx, dz = px - wx, pz - wz
                local dist = math.sqrt(dx*dx + dz*dz)

                if dist > 0.5 then
                    -- 向玩家移动
                    local speed = 3
                    local move_x = wx + (dx/dist) * speed * 0.1
                    local move_z = wz + (dz/dist) * speed * 0.1
                    whirlwind.Transform:SetPosition(move_x, 0, move_z)

                    -- 检查碰撞
                    if dist < 1.5 then
                        -- 击退和伤害
                        if nearest.components and nearest.components.health then
                            nearest.components.health:DoDelta(-35, nil, "leaf_whirlwind")  -- 秋季技能：35伤害（符合设计文档30-50范围）
                        end
                        if nearest.components and nearest.components.locomotor then
                            local knockback_dist = 3
                            local knock_x = px + (dx/dist) * knockback_dist
                            local knock_z = pz + (dz/dist) * knockback_dist
                            nearest.Transform:SetPosition(knock_x, 0, knock_z)
                        end
                        whirlwind:Remove()
                    end
                end
            end
        end)
    end
end

-- 创建秋实（秋季技能奖励）
local function CreateAutumnFruit(x, z)
    local fruit = SpawnPrefab("berries")  -- 复用浆果作为秋实
    if fruit then
        fruit.Transform:SetPosition(x, 0, z)
        fruit:AddTag("autumn_fruit")
        -- 修改拾取效果为回复理智
        if fruit.components.inventoryitem then
            local old_onpickup = fruit.components.inventoryitem.onpickupfn
            fruit.components.inventoryitem.onpickupfn = function(inst, picker)
                if picker and picker.components and picker.components.sanity then
                    picker.components.sanity:DoDelta(15)  -- 回复15理智
                end
                if old_onpickup then old_onpickup(inst, picker) end
            end
        end
        -- 10秒后消失
        fruit:DoTaskInTime(10, fruit.Remove)
    end
end

-- 创建冰斑（冬季技能）
local function CreateIcePatch(x, z)
    local ice = SpawnPrefab("ice")  -- 复用冰块作为冰斑
    if ice then
        ice.Transform:SetPosition(x, 0, z)
        ice:AddTag("ice_patch")
        ice.persists = false

        -- 添加滑步效果检测
        ice:DoPeriodicTask(0.1, function()
            local ents = TheSim:FindEntities(x, 0, z, 1.5, {"player"}, {"playerghost"})
            for _, player in ipairs(ents) do
                if player.components and player.components.locomotor then
                    -- 滑步效果：短暂失去控制并滑行
                    player.components.locomotor:SetExternalSpeedMultiplier(player, "ice_patch", 2)  -- 加速滑行
                    player:DoTaskInTime(0.5, function()
                        if player.components and player.components.locomotor then
                            player.components.locomotor:RemoveExternalSpeedMultiplier(player, "ice_patch")
                        end
                    end)
                end
            end
        end)

        -- 5秒后消失
        ice:DoTaskInTime(5, ice.Remove)
    end
end

local function DoPhaseSkill(inst)
    local phase = inst._phase or "spring"
    local x,y,z = inst.Transform:GetWorldPosition()
    if phase == "spring" then
        -- 春季：雷鸣号令 - 落雷预警圈 + 藤蔓缠绕点
        local target = FindEntity(inst, 20, function(guy) return guy:HasTag("player") and not guy:HasTag("playerghost") end)
        if target then
            local px,py,pz = target.Transform:GetWorldPosition()
            local warn = SpawnPrefab("fx_warning_circle"); if warn then warn.Transform:SetPosition(px,0,pz) end
            inst:DoTaskInTime(0.7, function()
                local ents = TheSim:FindEntities(px, py, pz, 3, {"player"}, {"playerghost"})
                for _,v in ipairs(ents) do
                    if v.components and v.components.health then v.components.health:DoDelta(-35, nil, inst.prefab) end  -- 春季技能：35伤害（符合设计文档30-50范围）
                    if v.components and v.components.moisture then v.components.moisture:DoDelta(10) end  -- 湿身效果
                    -- 短暂眩晕（减速模拟）
                    Slow(v, 0.5, 1)
                end
                local fx = SpawnPrefab("staff_castinglight"); if fx then fx.Transform:SetPosition(px,0,pz) end
            end)
        end

        -- 生成2个藤蔓缠绕点
        for i = 1, 2 do
            local angle = math.random() * 2 * math.pi
            local dist = 5 + math.random() * 5
            local trap_x = x + math.cos(angle) * dist
            local trap_z = z + math.sin(angle) * dist
            CreateVineTrap(trap_x, trap_z)
        end

    elseif phase == "summer" then
        -- 夏季：炎热涌动 - 灼热波 + Boss移速加成
        local warn = SpawnPrefab("fx_warning_circle"); if warn then warn.Transform:SetPosition(x,0,z); warn.Transform:SetScale(1.6,1.6,1.6) end
        inst:DoTaskInTime(0.7, function()
            local ents = TheSim:FindEntities(x,y,z, 6, {"player"}, {"playerghost"})
            for _,v in ipairs(ents) do
                if v.components and v.components.health then v.components.health:DoDelta(-40, nil, inst.prefab) end  -- 夏季技能：40伤害（符合设计文档30-50范围）
                if v.components and v.components.temperature then v.components.temperature:DoDelta(5) end
                -- 轻微DoT（灼伤）
                if not v.components.seasonal_debuff then
                    v:AddComponent("seasonal_debuff")
                end
                v.components.seasonal_debuff:ApplyBurn(2, 3)  -- 2点/秒，持续3秒
            end
            local fx = SpawnPrefab("staff_castinglight"); if fx then fx.Transform:SetPosition(x,0,z) end

            -- 点燃地面小范围（装饰性）
            for i = 1, 3 do
                local angle = math.random() * 2 * math.pi
                local dist = math.random() * 4
                local fire_x = x + math.cos(angle) * dist
                local fire_z = z + math.sin(angle) * dist
                local fire = SpawnPrefab("campfire_fire")
                if fire then
                    fire.Transform:SetPosition(fire_x, 0, fire_z)
                    fire:DoTaskInTime(2, fire.Remove)  -- 2秒后熄灭
                end
            end
        end)

        -- Boss获得短时移速加成
        if inst.components and inst.components.locomotor then
            inst.components.locomotor:SetExternalSpeedMultiplier(inst, "summer_boost", 1.1)  -- 10%移速加成
            inst:DoTaskInTime(5, function()
                if inst.components and inst.components.locomotor then
                    inst.components.locomotor:RemoveExternalSpeedMultiplier(inst, "summer_boost")
                end
            end)
        end

    elseif phase == "autumn" then
        -- 秋季：萧瑟旋风 - 落叶旋风 + 秋实掉落
        -- 生成2个落叶旋风
        for i = 1, 2 do
            local target = FindEntity(inst, 15, function(guy) return guy:HasTag("player") and not guy:HasTag("playerghost") end)
            if target then
                local px, py, pz = target.Transform:GetWorldPosition()
                CreateLeafWhirlwind(inst, px, pz)
            end
        end

        -- 掉落1-2个秋实
        local fruit_count = 1 + math.random(0, 1)
        for i = 1, fruit_count do
            local angle = math.random() * 2 * math.pi
            local dist = 3 + math.random() * 4
            local fruit_x = x + math.cos(angle) * dist
            local fruit_z = z + math.sin(angle) * dist
            CreateAutumnFruit(fruit_x, fruit_z)
        end

    elseif phase == "winter" then
        -- 冬季：寒潮吐息 - 锥形寒息 + 冰斑生成
        -- 寻找目标进行锥形攻击
        local target = FindEntity(inst, 12, function(guy) return guy:HasTag("player") and not guy:HasTag("playerghost") end)
        if target then
            local px, py, pz = target.Transform:GetWorldPosition()
            local dx, dz = px - x, pz - z
            local angle = math.atan2(dz, dx)

            -- 锥形范围攻击
            local warn = SpawnPrefab("fx_warning_circle"); if warn then warn.Transform:SetPosition(x,0,z); warn.Transform:SetScale(2,2,2) end
            inst:DoTaskInTime(0.7, function()
                for i = -2, 2 do  -- 5个方向的锥形
                    local cone_angle = angle + i * 0.3
                    for j = 1, 3 do  -- 3个距离层
                        local cone_x = x + math.cos(cone_angle) * j * 2
                        local cone_z = z + math.sin(cone_angle) * j * 2
                        local ents = TheSim:FindEntities(cone_x, 0, cone_z, 1.5, {"player"}, {"playerghost"})
                        for _, v in ipairs(ents) do
                            if v.components and v.components.health then v.components.health:DoDelta(-30, nil, inst.prefab) end  -- 冬季技能：30伤害（符合设计文档30-50范围）
                            Slow(v, 0.85, 2)  -- 缓速
                            if v.components and v.components.temperature then v.components.temperature:DoDelta(-10) end  -- 体温下降
                        end
                    end
                end
                local fx = SpawnPrefab("staff_castinglight"); if fx then fx.Transform:SetPosition(x,0,z) end
            end)
        end

        -- 生成3-4个冰斑
        local ice_count = 3 + math.random(0, 1)
        for i = 1, ice_count do
            local angle = math.random() * 2 * math.pi
            local dist = 4 + math.random() * 6
            local ice_x = x + math.cos(angle) * dist
            local ice_z = z + math.sin(angle) * dist
            CreateIcePatch(ice_x, ice_z)
        end
    end
end

local function KeepTarget(inst, target)
    return inst:IsNear(target, 30)
end

local function OnAttacked(inst, data)
    inst.components.combat:SetTarget(data.attacker)
    inst:DoPeriodicTask(6, DoPhaseSkill)

    -- 护盾减免：被击中后按比例回补，等效吸收伤害
    -- 设计文档：护甲0.8表示80%减免，即吸收80%伤害
    if inst._shielded and data and data.damage and data.damage > 0 and inst.components.health then
        local absorb = (TUNING.SEASON_BOSS_SHIELD_ABSORB or 0.8)  -- 80%减免
        inst.components.health:DoDelta(data.damage * absorb, true, "season_shield")
    end

    -- 季伤弱点：破盾后更容易受到季节效果
    if not inst._shielded and data and data.attacker then
        local attacker = data.attacker
        local season = "autumn" -- 默认值

        -- 检查攻击者的季节刻印
        if attacker.components and attacker.components.season_engraving then
            season = attacker.components.season_engraving:GetSeason() or "autumn"
        else
            -- 使用世界季节作为后备
            season = TheWorld.state.season or "autumn"
        end

        -- 应用增强的季节效果
        if season == "spring" and inst.GetIsWet and inst:GetIsWet() then
            -- 春季：对湿身Boss额外伤害
            if inst.components and inst.components.health then
                inst.components.health:DoDelta(-5, nil, "season_weakness")
            end
        elseif season == "summer" then
            -- 夏季：灼伤效果增强
            if not inst.components.seasonal_debuff then
                inst:AddComponent("seasonal_debuff")
            end
            inst.components.seasonal_debuff:ApplyBurn(3, 2)  -- 3点/秒，持续2秒
        elseif season == "autumn" then
            -- 秋季：减速效果增强
            if not inst.components.seasonal_debuff then
                inst:AddComponent("seasonal_debuff")
            end
            inst.components.seasonal_debuff:ApplySlow(0.7, 2)  -- 30%减速，持续2秒
        elseif season == "winter" then
            -- 冬季：冰冻效果（强化减速）
            if not inst.components.seasonal_debuff then
                inst:AddComponent("seasonal_debuff")
            end
            inst.components.seasonal_debuff:ApplySlow(0.6, 3)  -- 40%减速，持续3秒
        end
    end
end

local function OnHealthDelta(inst, data)
    local pct = inst.components.health:GetPercent()
    local idx = math.floor((1-pct) * 4) + 1
    idx = math.min(math.max(idx,1),4)
    local phase = PHASES[idx]
    if phase ~= inst._phase then
        inst._phase = phase
        SetShield(inst, true)
    end
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    inst.Transform:SetSixFaced()

    inst.AnimState:SetBank("leif")
    inst.AnimState:SetBuild("leif")
    inst.AnimState:PlayAnimation("idle_loop", true)

    inst:AddTag("epic")
    inst:AddTag("season_warden")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst._phase = inst._initial_weakness or "spring"  -- 使用祭坛设置的初始弱点，默认春季
    inst._shielded = true

    inst:AddComponent("inspectable")

    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(TUNING.SEASON_BOSS_HP or 8000)

    inst:AddComponent("lootdropper")
    -- 设置Boss掉落物（按设计文档：季芯x2、松露/蜂王浆/当季成品食物随机各1）
    inst.components.lootdropper:SetLoot({"season_core", "season_core"})  -- 固定掉落2个季芯
    inst.components.lootdropper:AddRandomLoot("red_cap", 0.8)  -- 松露类物品（使用红蘑菇代替，80%概率）
    inst.components.lootdropper:AddRandomLoot("royal_jelly", 0.8)  -- 蜂王浆（80%概率）
    -- 当季成品食物（动态根据死亡时的季节决定）
    inst.components.lootdropper.droplootfn = function(inst)
        local loots = {"season_core", "season_core"}  -- 固定季芯x2

        -- 随机掉落松露类物品（80%概率）
        if math.random() < 0.8 then
            table.insert(loots, "red_cap")
        end

        -- 随机掉落蜂王浆（80%概率）
        if math.random() < 0.8 then
            table.insert(loots, "royal_jelly")
        end

        -- 当季成品食物
        local seasonal_foods = {
            spring = "asparagus_cooked",
            summer = "watermelon_cooked",
            autumn = "pumpkin_cooked",
            winter = "blue_cap_cooked"
        }
        local current_season = TheWorld.state.season or "autumn"
        local seasonal_food = seasonal_foods[current_season] or "berries_cooked"
        table.insert(loots, seasonal_food)

        return loots
    end

    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(75)
    inst.components.combat:SetAttackPeriod(2.5)
    inst.components.combat:SetRetargetFunction(3, Retarget)
    inst.components.combat:SetKeepTargetFunction(KeepTarget)

    inst:ListenForEvent("attacked", OnAttacked)
    inst:ListenForEvent("healthdelta", OnHealthDelta)
    inst:ListenForEvent("season_sigil", OnSigil)

    inst:SetStateGraph("SGleif")
    inst:SetBrain(require "brains/leifbrain")

    return inst
end

return Prefab("boss_season_warden", fn, assets, prefabs)
