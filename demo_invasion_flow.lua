-- 野外入侵完整流程演示脚本
-- 使用方法：在游戏控制台中执行 dofile("mods/thinking/demo_invasion_flow.lua")

local function DemoInvasionFlow()
    print("=== 野外入侵完整流程演示 ===")
    
    if not TheWorld.ismastersim then
        print("❌ 必须在服务端运行此演示")
        return
    end
    
    local invasion_manager = TheWorld.components.season_warden_invasion
    if not invasion_manager then
        print("❌ 未找到入侵管理器组件")
        return
    end
    
    local player = ThePlayer or AllPlayers[1]
    if not player then
        print("❌ 未找到玩家")
        return
    end
    
    print("✓ 准备演示野外入侵流程")
    
    -- 1. 重置状态
    print("\n--- 步骤1：重置入侵状态 ---")
    invasion_manager.invasions_done = 0
    invasion_manager.last_failed_day = nil
    invasion_manager.active = false
    if invasion_manager.boss and invasion_manager.boss:Is<PERSON>alid() then
        invasion_manager.boss:Remove()
    end
    invasion_manager.boss = nil
    invasion_manager.origin = nil
    print("✓ 入侵状态已重置")
    
    -- 2. 显示当前配置
    print("\n--- 步骤2：当前配置 ---")
    print("每季入侵次数:", invasion_manager.per_season)
    print("血量倍率:", invasion_manager.hp_mul)
    print("掉落倍率:", invasion_manager.loot_mul)
    print("战斗半径:", invasion_manager.battle_radius)
    print("预警时间:", invasion_manager.warn_secs, "秒")
    
    local player_count = #(AllPlayers or {})
    local multiplayer_scale = player_count > 1 and math.sqrt(player_count) or 1.0
    print("玩家数量:", player_count)
    print("多人缩放:", string.format("%.2f", multiplayer_scale))
    
    -- 3. 模拟入侵触发
    print("\n--- 步骤3：触发入侵 ---")
    print("正在寻找合适的入侵位置...")
    
    -- 手动调用入侵逻辑
    invasion_manager:TryStartInvasion()
    
    if invasion_manager.active then
        print("✓ 入侵已激活，Boss将在", invasion_manager.warn_secs, "秒后降临")
        
        -- 4. 等待Boss生成
        print("\n--- 步骤4：等待Boss生成 ---")
        print("请等待", invasion_manager.warn_secs, "秒...")
        
        -- 设置一个任务来检查Boss状态
        TheWorld:DoTaskInTime(invasion_manager.warn_secs + 1, function()
            if invasion_manager.boss and invasion_manager.boss:IsValid() then
                local boss = invasion_manager.boss
                print("✓ Boss已生成！")
                print("Boss血量:", boss.components.health.currenthealth, "/", boss.components.health.maxhealth)
                print("Boss位置:", string.format("(%.1f, %.1f)", boss.Transform:GetWorldPosition()))
                print("战斗半径:", invasion_manager.battle_radius, "格")
                
                -- 5. 演示战斗半径机制
                print("\n--- 步骤5：战斗半径演示 ---")
                print("如果Boss离开原点超过", invasion_manager.battle_radius, "格，将会：")
                print("1. 返回原点")
                print("2. 回血至开场血量的50%")
                print("3. 设置同日不再追击")
                print("4. 2天后重试入侵")
                
                -- 6. 演示多人缩放
                print("\n--- 步骤6：多人缩放演示 ---")
                local base_hp = 8000 * invasion_manager.hp_mul
                local scaled_hp = base_hp * multiplayer_scale
                print("基础血量:", base_hp)
                print("缩放后血量:", math.floor(scaled_hp))
                print("掉落缩放:", string.format("%.1f倍", multiplayer_scale))
                
                -- 7. 提供测试命令
                print("\n--- 步骤7：测试命令 ---")
                print("传送到Boss位置: c_goto(TheWorld.components.season_warden_invasion.boss)")
                print("击杀Boss: c_exec(TheWorld.components.season_warden_invasion.boss.components.health:Kill())")
                print("让Boss逃脱: c_exec(TheWorld.components.season_warden_invasion.boss.Transform:SetPosition(999, 0, 999))")
                print("查看入侵状态: c_exec(print('完成次数:', TheWorld.components.season_warden_invasion.invasions_done, '/', TheWorld.components.season_warden_invasion.per_season))")
                
            else
                print("❌ Boss生成失败")
            end
        end)
        
    else
        print("❌ 入侵触发失败，可能的原因：")
        print("- 找不到合适的入侵位置")
        print("- 本季入侵次数已达上限")
        print("- 今日已有入侵失败")
        
        if invasion_manager.invasions_done >= invasion_manager.per_season then
            print("本季已完成入侵次数:", invasion_manager.invasions_done, "/", invasion_manager.per_season)
        end
        
        local current_day = math.floor(GetTime() / (TUNING.TOTAL_DAY_TIME or 480))
        if invasion_manager.last_failed_day == current_day then
            print("今日已有入侵失败，同日不再追击")
        end
    end
    
    -- 8. 提供调试命令
    print("\n--- 调试命令 ---")
    print("查看详细状态: dofile('mods/thinking/test_invasion_system.lua')")
    print("重置入侵状态: c_exec(local m = TheWorld.components.season_warden_invasion; m.invasions_done = 0; m.last_failed_day = nil; m.active = false)")
    print("强制生成Boss: c_spawn('boss_season_warden')")
    
    print("\n=== 演示设置完成 ===")
end

-- 执行演示
DemoInvasionFlow()
