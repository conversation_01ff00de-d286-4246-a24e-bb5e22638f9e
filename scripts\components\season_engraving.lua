local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local net_string = _G.net_string
local ACTIONS = _G.ACTIONS
local TUNING = _G.TUNING
local SpawnPrefab = _G.SpawnPrefab

local SeasonEngraving = Class(function(self, inst)
    self.inst = inst
    self.current = nil -- "spring"|"summer"|"autumn"|"winter"
    self.manual_season = nil -- 手动设置的季节，在当前世界季节内有效
    self.last_world_season = nil -- 记录上次的世界季节
    self._net = nil
    self._sanityhook = nil
    self._old_hunger_rate = nil
    self._old_work_multipliers = {}

    if not TheWorld.ismastersim then
        -- 客户端：监听同步事件
        self._net = net_string(inst.GUID, "season_engraving.current", "season_engraving_dirty")
        inst:ListenForEvent("season_engraving_dirty", function()
            self.current = self._net:value()
        end)
        return
    end

    -- 服务端：定期刷新
    inst:DoTaskInTime(0, function() self:Refresh() end)
    inst:WatchWorldState("season", function()
        -- 世界季节变化时总是刷新，但会检查是否需要清除手动设置
        self:OnWorldSeasonChanged()
    end)
end)

function SeasonEngraving:ApplyBuffs(inst, season)
    if not inst.components then return end

    -- 清理所有旧效果
    self:ClearBuffs(inst)

    if season == "spring" then
        self:ApplySpringBuffs(inst)
    elseif season == "summer" then
        self:ApplySummerBuffs(inst)
    elseif season == "autumn" then
        self:ApplyAutumnBuffs(inst)
    elseif season == "winter" then
        self:ApplyWinterBuffs(inst)
    end
end

function SeasonEngraving:ClearBuffs(inst)
    -- 清理移动速度
    if inst.components.locomotor then
        inst.components.locomotor:SetExternalSpeedMultiplier(inst, "season_eng", 1)
    end

    -- 清理饥饿速率
    if inst.components.hunger and self._old_hunger_rate then
        inst.components.hunger:SetRate(self._old_hunger_rate)
        self._old_hunger_rate = nil
    end

    -- 清理温度隔热
    if inst.components.temperature then
        inst.components.temperature.inherentinsulation = 0
        inst.components.temperature.inherentsummerinsulation = 0
    end

    -- 清理防水
    if inst.components.waterproofer then
        inst.components.waterproofer:SetEffectiveness(0)
    end

    -- 清理工作效率
    if inst.components.workmultiplier then
        for action, _ in pairs(self._old_work_multipliers) do
            inst.components.workmultiplier:RemoveMultiplier(action, "season_eng")
        end
        self._old_work_multipliers = {}
    end

    -- 清理理智钩子
    if self._sanityhook then
        inst:RemoveEventCallback("sanitydelta", self._sanityhook)
        self._sanityhook = nil
    end

    -- 清理收获钩子
    if self._harvesthook then
        inst:RemoveEventCallback("picksomething", self._harvesthook)
        self._harvesthook = nil
    end

    -- 清理火焰伤害减免
    if inst.components.health then
        inst.components.health.fire_damage_scale = 1.0
    end
end

function SeasonEngraving:ApplySpringBuffs(inst)
    -- 防水：通过添加waterproofer组件或设置标签
    if not inst.components.waterproofer then
        inst:AddComponent("waterproofer")
    end
    inst.components.waterproofer:SetEffectiveness(TUNING.SEASON_CRAFTER_SPRING_WATERPROOF or 0.5)

    -- 采集/采矿/砍伐效率 +50%
    if not inst.components.workmultiplier then
        inst:AddComponent("workmultiplier")
    end
    local work_mult = TUNING.SEASON_CRAFTER_SPRING_WORK_MULT or 1.5
    inst.components.workmultiplier:AddMultiplier(ACTIONS.CHOP, work_mult, "season_eng")
    inst.components.workmultiplier:AddMultiplier(ACTIONS.MINE, work_mult, "season_eng")
    inst.components.workmultiplier:AddMultiplier(ACTIONS.PICK, work_mult, "season_eng")
    self._old_work_multipliers[ACTIONS.CHOP] = true
    self._old_work_multipliers[ACTIONS.MINE] = true
    self._old_work_multipliers[ACTIONS.PICK] = true

    -- 湿冷理智损失 -50%
    if inst.components.sanity then
        local protect_mult = TUNING.SEASON_CRAFTER_SPRING_SANITY_PROTECT or 0.5
        self._sanityhook = function(inst2, data)
            if data and data.amount and data.amount < 0 and inst2.components and inst2.components.moisture then
                local moisture_pct = inst2.components.moisture:GetMoisturePercent()
                if moisture_pct > 0.1 then
                    local reduce = -data.amount * protect_mult
                    inst2.components.sanity:DoDelta(reduce, true, "season_spring_moist")
                end
            end
        end
        inst:ListenForEvent("sanitydelta", self._sanityhook)
    end
end

function SeasonEngraving:ApplySummerBuffs(inst)
    -- 过热隔热 +60
    if inst.components.temperature then
        inst.components.temperature.inherentsummerinsulation = TUNING.SEASON_CRAFTER_SUMMER_INSULATION or 60
    end

    -- 移动速度 +20%
    if inst.components.locomotor then
        inst.components.locomotor:SetExternalSpeedMultiplier(inst, "season_eng", TUNING.SEASON_CRAFTER_SUMMER_SPEED_MULT or 1.2)
    end

    -- 火焰伤害 -50%
    if inst.components.health then
        inst.components.health.fire_damage_scale = TUNING.SEASON_CRAFTER_SUMMER_FIRE_RESIST or 0.5
    end
end

function SeasonEngraving:ApplyAutumnBuffs(inst)
    -- 饥饿消耗 -50%
    if inst.components.hunger then
        self._old_hunger_rate = inst.components.hunger:GetRate()
        inst.components.hunger:SetRate(self._old_hunger_rate * (TUNING.SEASON_CRAFTER_AUTUMN_HUNGER_MULT or 0.5))
    end

    -- 收获增益：50%概率+1产出
    self._harvesthook = function(inst2, data)
        if data and data.object and data.picker == inst2 then
            local obj = data.object
            -- 检查是否为浆果丛或作物类
            local is_harvestable = false
            if obj.prefab then
                local prefab = obj.prefab
                is_harvestable = prefab:find("berry") or prefab:find("farm") or prefab:find("crop") or
                               prefab == "grass" or prefab == "sapling" or prefab == "reeds" or
                               (obj.components and obj.components.pickable)
            end

            if is_harvestable and math.random() < (TUNING.SEASON_CRAFTER_AUTUMN_HARVEST_CHANCE or 0.5) then
                -- 50%概率额外掉落：尝试从对象的loot或pickable获取产物
                local extra_item = nil
                if obj.components and obj.components.pickable and obj.components.pickable.product then
                    extra_item = obj.components.pickable.product
                elseif obj.components and obj.components.lootdropper then
                    local loot = obj.components.lootdropper:GenerateLoot()
                    if loot and #loot > 0 then
                        extra_item = loot[1].prefab
                    end
                end

                if extra_item then
                    local extra = SpawnPrefab(extra_item)
                    if extra then
                        local x, y, z = obj.Transform:GetWorldPosition()
                        extra.Transform:SetPosition(x + math.random(-1, 1), 0, z + math.random(-1, 1))
                    end
                end
            end
        end
    end
    inst:ListenForEvent("picksomething", self._harvesthook)
end

function SeasonEngraving:ApplyWinterBuffs(inst)
    -- 寒冷隔热 +120
    if inst.components.temperature then
        inst.components.temperature.inherentinsulation = TUNING.SEASON_CRAFTER_WINTER_INSULATION or 120
    end

    -- 冰缓打击通过武器实现，这里不处理
end

function SeasonEngraving:GetSeason()
    return self.current
end

-- 手动设置季节刻印
function SeasonEngraving:SetSeason(season)
    if not TheWorld.ismastersim then return end

    local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
    if not valid_seasons[season] then
        print("Invalid season:", season)
        return false
    end

    self.manual_season = season
    self.current = season

    if self._net == nil then
        self._net = net_string(self.inst.GUID, "season_engraving.current", "season_engraving_dirty")
    end
    self._net:set(season)

    -- 播放切换特效
    if self.inst:IsValid() then
        self.inst:PushEvent("season_engraving_fx", {season = season})
    end

    -- 应用新的季节效果
    self:ApplyBuffs(self.inst, season)

    return true
end

-- 清除手动设置，回到跟随世界季节
function SeasonEngraving:ClearManualSeason()
    if not TheWorld.ismastersim then return end

    self.manual_season = nil
    self:Refresh(true)  -- 重新跟随世界季节
end

-- 检查是否为手动设置的季节
function SeasonEngraving:IsManualSeason()
    return self.manual_season ~= nil
end

-- 处理世界季节变化
function SeasonEngraving:OnWorldSeasonChanged()
    if not TheWorld.ismastersim then return end

    local current_world_season = TheWorld.state.season

    -- 如果世界季节发生了变化，清除手动设置
    if self.last_world_season and self.last_world_season ~= current_world_season then
        self.manual_season = nil
        if self.inst.components and self.inst.components.talker then
            self.inst.components.talker:Say("季节变化，刻印重置...")
        end
    end

    -- 更新记录的世界季节
    self.last_world_season = current_world_season

    -- 刷新季节刻印
    self:Refresh(true)
end

function SeasonEngraving:Refresh(play_fx)
    if not TheWorld.ismastersim then return end

    local world_season = TheWorld.state.season

    -- 初始化记录的世界季节
    if not self.last_world_season then
        self.last_world_season = world_season
    end

    -- 优先使用手动设置的季节，否则使用世界季节
    local season = self.manual_season or world_season -- "spring"|"summer"|"autumn"|"winter"
    self.current = season

    if self._net == nil then
        self._net = net_string(self.inst.GUID, "season_engraving.current", "season_engraving_dirty")
    end
    self._net:set(season)

    if play_fx and self.inst:IsValid() and self.inst.components and self.inst.components.locomotor then
        -- 简易FX占位：可在角色prefab中生成更合适的FX
        self.inst:PushEvent("season_engraving_fx", {season = season})
    end

    self:ApplyBuffs(self.inst, season)
end

return SeasonEngraving
