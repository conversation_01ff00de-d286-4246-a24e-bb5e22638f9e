-- 季节符印使用演示脚本
-- 展示如何使用季节符印来破Boss的盾

local function DemoSeasonSealUsage()
    print("=== 季节符印使用演示 ===")
    
    local player = ThePlayer or AllPlayers[1]
    if not player then
        print("❌ 未找到玩家")
        return
    end
    
    -- 检查是否为季匠角色
    if not player.components.season_engraving then
        print("❌ 当前角色不是季匠，无法使用季节符印")
        print("请切换到季匠角色或给当前角色添加季节刻印组件")
        return
    end
    
    print("✓ 检测到季匠角色")
    
    -- 1. 演示场景设置
    print("\n--- 演示场景设置 ---")
    
    -- 生成Boss
    local boss = SpawnPrefab("boss_season_warden")
    if boss then
        local px, py, pz = player.Transform:GetWorldPosition()
        boss.Transform:SetPosition(px + 5, 0, pz)
        print("✓ 生成季冠树守Boss")
        
        -- 设置Boss为春季阶段
        boss._phase = "spring"
        boss._shielded = true
        boss:AddTag("season_shield_immune")
        print("✓ Boss设置为春季阶段，护盾开启")
    else
        print("❌ 无法生成Boss")
        return
    end
    
    -- 生成季节之刃
    local blade = SpawnPrefab("season_blade")
    if blade then
        player.components.inventory:GiveItem(blade)
        player.components.inventory:Equip(blade)
        print("✓ 装备季节之刃")
    else
        print("❌ 无法生成季节之刃")
        return
    end
    
    -- 2. 演示问题场景
    print("\n--- 演示问题场景 ---")
    
    local current_season = player.components.season_engraving:GetSeason()
    local world_season = TheWorld.state.season
    
    print("当前世界季节: " .. (world_season or "未知"))
    print("玩家季节刻印: " .. (current_season or "未知"))
    print("Boss护盾季节: " .. (boss._phase or "未知"))
    
    if current_season ~= boss._phase then
        print("❌ 季节不匹配！无法用武器破盾")
        print("需要切换季节刻印到: " .. (boss._phase or "未知"))
    else
        print("✓ 季节匹配，可以破盾")
    end
    
    -- 3. 演示解决方案
    print("\n--- 演示解决方案：使用季节符印 ---")
    
    -- 生成对应的季节符印
    local needed_season = boss._phase
    local seal = SpawnPrefab("season_seal_" .. needed_season)
    
    if seal then
        player.components.inventory:GiveItem(seal)
        print("✓ 获得" .. needed_season .. "季节符印")
        
        -- 使用符印切换季节
        print("使用符印切换季节刻印...")
        local success = seal.components.useableitem:OnUsed(player)
        
        if success then
            local new_season = player.components.season_engraving:GetSeason()
            print("✓ 成功切换到: " .. (new_season or "未知"))
            
            -- 验证现在可以破盾
            if new_season == boss._phase then
                print("✓ 季节匹配！现在可以用武器破Boss的盾了")
                
                -- 演示武器破盾
                print("\n--- 演示武器破盾 ---")
                print("模拟连续攻击Boss 3次...")
                
                for i = 1, 3 do
                    -- 模拟武器爆发攻击
                    boss:PushEvent("season_sigil", {source = "weapon_burst"})
                    print("第" .. i .. "次攻击")
                end
                
                if not boss._shielded then
                    print("✓ 成功破盾！Boss护盾已被摧毁")
                else
                    print("❌ 破盾失败，可能需要检查实现")
                end
            else
                print("❌ 季节切换失败")
            end
        else
            print("❌ 符印使用失败")
        end
    else
        print("❌ 无法生成季节符印")
    end
    
    -- 4. 演示其他季节符印
    print("\n--- 演示其他季节符印 ---")
    
    local seasons = {"spring", "summer", "autumn", "winter"}
    local season_names = {
        spring = "春季",
        summer = "夏季",
        autumn = "秋季",
        winter = "冬季"
    }
    
    print("生成所有季节符印供测试...")
    for _, season in ipairs(seasons) do
        local test_seal = SpawnPrefab("season_seal_" .. season)
        if test_seal then
            player.components.inventory:GiveItem(test_seal)
            print("✓ 获得" .. season_names[season] .. "符印")
        end
    end
    
    -- 5. 演示制作配方
    print("\n--- 制作配方说明 ---")
    print("季节符印制作配方：")
    print("- 春季符印：纸莎草x1 + 春季碎片x2")
    print("- 夏季符印：纸莎草x1 + 夏季碎片x2") 
    print("- 秋季符印：纸莎草x1 + 秋季碎片x2")
    print("- 冬季符印：纸莎草x1 + 冬季碎片x2")
    print("在季工坊台制作")
    
    -- 6. 使用提示
    print("\n--- 使用提示 ---")
    print("1. 右键点击季节符印使用")
    print("2. 符印为一次性消耗品")
    print("3. 只有季匠角色可以使用")
    print("4. 手动设置的季节优先于世界季节")
    print("5. 可以随时切换到任意季节")
    print("6. 影响武器效果和Boss破盾机制")
    
    print("\n=== 演示完成 ===")
    print("现在您可以：")
    print("- 使用不同季节符印切换刻印")
    print("- 测试与Boss的战斗")
    print("- 验证武器破盾机制")
end

return DemoSeasonSealUsage
