-- 季节符印 - 允许季匠主动切换季节刻印的物品

local assets = {}
local prefabs = {}

local SEASONS = {"spring", "summer", "autumn", "winter"}

local function MakeSeasonSeal(season)
    local name = "season_seal_" .. season
    
    local function fn()
        local inst = CreateEntity()
        
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()
        
        MakeInventoryPhysics(inst)
        
        -- 使用简单的图标（复用现有资源）
        inst.AnimState:SetBank("trinket_6")
        inst.AnimState:SetBuild("trinket_6") 
        inst.AnimState:PlayAnimation("idle")
        
        -- 根据季节设置颜色
        local colors = {
            spring = {0.5, 1, 0.5, 1},    -- 绿色
            summer = {1, 0.7, 0.3, 1},    -- 橙色
            autumn = {0.8, 0.5, 0.3, 1},  -- 褐色
            winter = {0.7, 0.9, 1, 1}     -- 蓝色
        }
        local color = colors[season] or {1, 1, 1, 1}
        inst.AnimState:SetMultColour(color[1], color[2], color[3], color[4])
        
        inst:AddTag("season_seal")
        inst:AddTag("season_seal_" .. season)
        
        inst.entity:SetPristine()
        
        if not TheWorld.ismastersim then
            return inst
        end
        
        inst._season = season
        
        inst:AddComponent("inspectable")
        inst.components.inspectable.getstatus = function(inst)
            local season_names = {
                spring = "春季",
                summer = "夏季", 
                autumn = "秋季",
                winter = "冬季"
            }
            return season_names[season] .. "符印，可切换季节刻印至" .. season_names[season]
        end
        
        inst:AddComponent("inventoryitem")
        
        -- 添加使用功能 - 右键使用
        inst:AddComponent("useableitem")
        inst.components.useableitem:SetOnUseFn(function(inst, user)
            -- 检查使用者是否有季节刻印组件
            if not user.components or not user.components.season_engraving then
                if user.components and user.components.talker then
                    user.components.talker:Say("我无法使用这个符印...")
                end
                return false
            end

            -- 切换季节刻印
            local success = user.components.season_engraving:SetSeason(season)

            if success then
                -- 播放特效和音效
                if user.SoundEmitter then
                    user.SoundEmitter:PlaySound("dontstarve/common/staff_spell")
                end

                local fx = SpawnPrefab("staff_castinglight")
                if fx then
                    fx.Transform:SetPosition(user.Transform:GetWorldPosition())
                    -- 设置特效颜色
                    if fx.AnimState then
                        fx.AnimState:SetMultColour(color[1], color[2], color[3], color[4])
                    end
                end

                -- 角色说话提示
                if user.components and user.components.talker then
                    local season_names = {
                        spring = "春之力量！",
                        summer = "夏之力量！",
                        autumn = "秋之力量！",
                        winter = "冬之力量！"
                    }
                    user.components.talker:Say(season_names[season] or "季节之力！")
                end

                -- 消耗物品（一次性使用）
                inst:Remove()
                return true
            else
                return false
            end
        end)
        
        return inst
    end
    
    return Prefab(name, fn, assets, prefabs)
end

-- 创建四个季节的符印
local prefabs_to_return = {}
for _, season in ipairs(SEASONS) do
    table.insert(prefabs_to_return, MakeSeasonSeal(season))
end

return unpack(prefabs_to_return)
