-- 配置项一致性测试
print("=== 配置项一致性测试 ===")

-- 模拟配置项函数
local function GetModConfigData(name)
    local configs = {
        boss_hp_mult = 1.2,
        boss_shield_absorb = 0.7,
        blade_fx_strength = 2,
        cloak_strength = 0,
        blade_stack_strength = 2,
        invasion_hp_mul = 0.4,
        invasion_loot_mul = 0.75,
        altar_cooldown_days = 7,
        gust_frequency = 3,
        vfx_strength = 1
    }
    return configs[name]
end

-- 测试Boss相关配置
print("\n--- Boss相关配置 ---")
local base_hp = 8000
local hp_mult = GetModConfigData('boss_hp_mult') or 1.0
print('Boss血量倍率配置:', hp_mult)
print('Boss基础血量:', base_hp, '-> 最终血量:', base_hp * hp_mult)

local shield_absorb = GetModConfigData('boss_shield_absorb') or 0.8
print('Boss护盾减免配置:', shield_absorb)
print('100点伤害 -> 实际减免:', 100 * shield_absorb, '点，剩余伤害:', 100 * (1 - shield_absorb), '点')

-- 测试入侵Boss配置
print("\n--- 入侵Boss配置 ---")
local invasion_hp_mul = GetModConfigData('invasion_hp_mul') or 0.4
local invasion_loot_mul = GetModConfigData('invasion_loot_mul') or 0.5
print('入侵HP倍率:', invasion_hp_mul)
print('入侵掉落倍率:', invasion_loot_mul)
print('入侵Boss血量:', base_hp * hp_mult * invasion_hp_mul)
print('入侵Boss掉落季芯数量:', math.max(1, math.floor(invasion_loot_mul * 2 + 0.5)))

-- 测试装备效果配置
print("\n--- 装备效果配置 ---")
local blade_fx = GetModConfigData('blade_fx_strength') or 1
local fx_mult = blade_fx == 0 and 0.7 or (blade_fx == 2 and 1.3 or 1.0)
print('季刃特效强度配置:', blade_fx, '-> 效果倍率:', fx_mult)

local cloak_strength = GetModConfigData('cloak_strength') or 1
local cloak_mult = cloak_strength == 0 and 0.7 or (cloak_strength == 2 and 1.3 or 1.0)
print('披风属性强度配置:', cloak_strength, '-> 属性倍率:', cloak_mult)

local stack_strength = GetModConfigData('blade_stack_strength') or 1
local stack_mult = stack_strength == 0 and 0.8 or (stack_strength == 2 and 1.2 or 1.0)
print('叠层爆发强度配置:', stack_strength, '-> 伤害倍率:', stack_mult)

-- 测试具体数值计算
print("\n--- 具体数值示例 ---")
print('春季爆发伤害（对Boss）:', 30 * stack_mult)
print('夏季爆发伤害:', 25 * stack_mult)
print('秋季爆发伤害（对Boss）:', 15 * stack_mult)
print('冬季爆发伤害:', 20 * stack_mult)

print('春季潮湿额外伤害:', 34 * 0.25 * fx_mult)
print('夏季灼伤伤害（对Boss）:', 2 * fx_mult, '点/秒')
print('秋季耐久返还概率:', 0.15 * fx_mult * 100, '%')

print('披风春季防水效果:', 0.4 * cloak_mult)
print('披风夏季隔热值:', 90 * cloak_mult)
print('披风秋季饥饿减免:', 0.05 * cloak_mult * 100, '%')
print('披风冬季保温值:', 150 * cloak_mult)

-- 测试事件配置
print("\n--- 事件配置 ---")
local altar_cooldown = GetModConfigData('altar_cooldown_days') or 5
local gust_freq = GetModConfigData('gust_frequency') or 2
print('祭坛冷却天数:', altar_cooldown)
print('季风乱流频率（每季）:', gust_freq)

-- 测试VFX配置
print("\n--- VFX配置 ---")
local vfx_strength = GetModConfigData('vfx_strength') or 1
local vfx_alpha = vfx_strength == 0 and 0.4 or (vfx_strength == 2 and 0.9 or 0.7)
local vfx_scale = vfx_strength == 0 and 1.0 or (vfx_strength == 2 and 1.8 or 1.2)
local vfx_vol = vfx_strength == 0 and 0.3 or (vfx_strength == 2 and 1.0 or 0.6)
print('VFX强度配置:', vfx_strength)
print('VFX透明度:', vfx_alpha, '缩放:', vfx_scale, '音量:', vfx_vol)

print("\n=== 测试完成 ===")
